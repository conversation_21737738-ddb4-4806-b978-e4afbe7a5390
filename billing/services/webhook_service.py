"""
Webhook handling service for Stripe events
"""
import stripe
import logging
from .base import BaseStripeService, WebhookHandlerInterface
from ..exceptions import WebhookError, WebhookSignatureError
from ..config import BillingConfig
from ..constants import PaymentStatus

logger = logging.getLogger(__name__)


class WebhookService(BaseStripeService, WebhookHandlerInterface):
    """Service for handling Stripe webhook events"""
    
    def handle_event(self, event):
        """Handle a webhook event"""
        try:
            event_type = event.get('type') if isinstance(event, dict) else event.type
            self._log_operation(f"Processing webhook event: {event_type}")
            
            # Get handler method
            handler_method = self._get_event_handler(event_type)
            if not handler_method:
                logger.warning(f"No handler found for event type: {event_type}")
                return False
            
            # Extract event data
            event_data = event.get('data', {}).get('object') if isinstance(event, dict) else event.data.object
            
            # Call handler
            result = handler_method(event_data)
            
            self._log_operation(f"Successfully processed webhook event: {event_type}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing webhook event {event_type}: {str(e)}")
            raise WebhookError(f"Error processing webhook: {str(e)}")
    
    def verify_signature(self, payload, signature, secret):
        """Verify webhook signature"""
        try:
            event = stripe.Webhook.construct_event(
                payload, signature, secret,
                tolerance=BillingConfig.get_webhook_tolerance()
            )
            return event
        except stripe.error.SignatureVerificationError as e:
            raise WebhookSignatureError(f"Invalid webhook signature: {str(e)}")
    
    def verify_signature_with_fallback(self, payload, signature):
        """Verify webhook signature with fallback secrets"""
        stripe_keys = BillingConfig.get_stripe_keys()
        
        # Try production secret first
        try:
            return self.verify_signature(
                payload, signature, 
                stripe_keys['test_webhook_secret_billing']
            )
        except WebhookSignatureError:
            # Fall back to test secret
            try:
                return self.verify_signature(
                    payload, signature,
                    stripe_keys['test_webhook_secret']
                )
            except WebhookSignatureError as e:
                logger.error(f"Webhook signature verification failed with both secrets: {str(e)}")
                raise
    
    def handle_checkout_completed(self, session):
        """Handle checkout.session.completed event"""
        try:
            self._log_operation(f"Processing checkout session completed: {session.id}")
            
            # Get customer
            customer_service = self._get_customer_service()
            customer = customer_service.get_or_create_customer_for_session(session)
            
            metadata = getattr(session, 'metadata', {})
            mode = getattr(session, 'mode', None)
            
            # Route to appropriate handler based on metadata and mode
            if mode == 'subscription' and metadata.get('solution_id'):
                return self._handle_enterprise_solution_subscription(session, customer, metadata)
            elif mode == 'subscription' and metadata.get('price_id'):
                # Handle regular subscription services (including verification services)
                return self._handle_subscription_service_payment(session, customer, metadata)
            elif mode == 'payment' and metadata.get('service_id') and metadata.get('enterprise_id'):
                return self._handle_enterprise_service_payment(session, customer, metadata)
            elif metadata.get('payment_type') == 'dna_analysis':
                return self._handle_dna_analysis_payment(session, customer, metadata)
            elif metadata.get('payment_type') == 'appointment' and metadata.get('appointment_id'):
                # Handle telemedicine appointment payments
                return self._handle_appointment_payment(session, customer, metadata)
            elif mode == 'payment' and metadata.get('price_id'):
                # Handle regular service payments (including verification services)
                return self._handle_service_payment(session, customer, metadata)
            else:
                logger.warning(f"Unknown checkout session type for session {session.id}")
                return False
                
        except Exception as e:
            logger.error(f"Error handling checkout completed: {str(e)}")
            raise WebhookError(f"Error handling checkout completed: {str(e)}")
    
    def handle_payment_succeeded(self, payment_intent):
        """Handle payment_intent.succeeded event"""
        try:
            self._log_operation(f"Processing payment succeeded: {payment_intent.id}")
            
            # Update payment status if it exists
            payment_service = self._get_payment_service()
            try:
                payment = payment_service.get_payment_by_stripe_id(payment_intent.id)
                payment_service.update_payment_status(payment, PaymentStatus.COMPLETED)
                return True
            except Exception:
                # Payment might not exist in our system (e.g., enterprise payments)
                logger.info(f"No local payment found for Stripe payment intent {payment_intent.id}")
                return True
                
        except Exception as e:
            logger.error(f"Error handling payment succeeded: {str(e)}")
            return False
    
    def handle_payment_failed(self, payment_intent):
        """Handle payment_intent.payment_failed event"""
        try:
            self._log_operation(f"Processing payment failed: {payment_intent.id}")
            
            # Update payment status if it exists
            payment_service = self._get_payment_service()
            try:
                payment = payment_service.get_payment_by_stripe_id(payment_intent.id)
                payment_service.update_payment_status(payment, PaymentStatus.FAILED)
                return True
            except Exception:
                # Payment might not exist in our system
                logger.info(f"No local payment found for Stripe payment intent {payment_intent.id}")
                return True
                
        except Exception as e:
            logger.error(f"Error handling payment failed: {str(e)}")
            return False
    
    def handle_subscription_created(self, subscription):
        """Handle customer.subscription.created event"""
        try:
            self._log_operation(f"Processing subscription created: {subscription.id}")
            
            subscription_service = self._get_subscription_service()
            return subscription_service.handle_subscription_created(subscription)
            
        except Exception as e:
            logger.error(f"Error handling subscription created: {str(e)}")
            return False
    
    def handle_subscription_updated(self, subscription):
        """Handle customer.subscription.updated event"""
        try:
            self._log_operation(f"Processing subscription updated: {subscription.id}")
            
            subscription_service = self._get_subscription_service()
            return subscription_service.handle_subscription_updated(subscription)
            
        except Exception as e:
            logger.error(f"Error handling subscription updated: {str(e)}")
            return False
    
    def handle_subscription_deleted(self, subscription):
        """Handle customer.subscription.deleted event"""
        try:
            self._log_operation(f"Processing subscription deleted: {subscription.id}")
            
            subscription_service = self._get_subscription_service()
            return subscription_service.handle_subscription_deleted(subscription)
            
        except Exception as e:
            logger.error(f"Error handling subscription deleted: {str(e)}")
            return False
    
    def handle_subscription_quantity_updated(self, subscription):
        """Handle subscription quantity updates"""
        try:
            self._log_operation(f"Processing subscription quantity updated: {subscription.id}")
            
            subscription_service = self._get_subscription_service()
            return subscription_service.handle_subscription_quantity_updated(subscription)
            
        except Exception as e:
            logger.error(f"Error handling subscription quantity updated: {str(e)}")
            return False
    
    def handle_invoice_payment_succeeded(self, invoice):
        """Handle invoice.payment_succeeded event"""
        try:
            self._log_operation(f"Processing invoice payment succeeded: {invoice.id}")
            
            subscription_service = self._get_subscription_service()
            return subscription_service.handle_invoice_payment_succeeded(invoice)
            
        except Exception as e:
            logger.error(f"Error handling invoice payment succeeded: {str(e)}")
            return False
    
    def handle_invoice_payment_failed(self, invoice):
        """Handle invoice.payment_failed event"""
        try:
            self._log_operation(f"Processing invoice payment failed: {invoice.id}")
            
            subscription_service = self._get_subscription_service()
            return subscription_service.handle_invoice_payment_failed(invoice)
            
        except Exception as e:
            logger.error(f"Error handling invoice payment failed: {str(e)}")
            return False
    
    def _get_event_handler(self, event_type):
        """Get handler method for event type"""
        handler_mapping = BillingConfig.get_webhook_handlers()
        handler_name = handler_mapping.get(event_type)
        
        if handler_name:
            return getattr(self, handler_name, None)
        return None
    
    def _handle_enterprise_solution_subscription(self, session, customer, metadata):
        """Handle enterprise solution subscription"""
        enterprise_service = self._get_enterprise_service()
        return enterprise_service.handle_solution_subscription(session, customer, metadata)
    
    def _handle_enterprise_service_payment(self, session, customer, metadata):
        """Handle enterprise service payment"""
        enterprise_service = self._get_enterprise_service()
        return enterprise_service.handle_service_payment(session, customer, metadata)
    
    def _handle_dna_analysis_payment(self, session, customer, metadata):
        """Handle DNA analysis payment"""
        # Import here to avoid circular imports
        from ..views.dna_analysis_views import DNAAnalysisPaymentHandler
        handler = DNAAnalysisPaymentHandler()
        return handler.handle_payment_success(session, customer, metadata)

    def _handle_appointment_payment(self, session, customer, metadata):
        """Handle appointment payment (telemedicine peer-to-peer payments)"""
        try:
            appointment_service = self._get_appointment_payment_service()
            return appointment_service.handle_appointment_payment_success(session, metadata)
        except Exception as e:
            logger.error(f"Error handling appointment payment for session {session.id}: {str(e)}")
            return False

    def _handle_service_payment(self, session, customer, metadata):
        """Handle regular service payment (including verification services)"""
        try:
            self._log_operation(f"Processing service payment for session {session.id}")

            # Get the price from metadata
            price_id = metadata.get('price_id')
            if not price_id:
                logger.error(f"No price_id found in metadata for session {session.id}")
                return False

            # Get the price and service
            from ..models.product import Price

            try:
                price = Price.objects.get(id=price_id)
                product = price.product
                service = product.service if product.product_type == 'service' else None

                if not service:
                    logger.error(f"No service found for price_id {price_id}, product_type: {product.product_type}")
                    return False

            except Price.DoesNotExist:
                logger.error(f"Price not found for price_id {price_id}")
                return False
            except AttributeError as e:
                logger.error(f"Error accessing price/product/service for price_id {price_id}: {str(e)}")
                return False

            # Create service payment record
            payment_service = self._get_payment_service()
            payment = payment_service.create_service_payment(
                user=customer.user,
                service=service,
                amount=int(price.unit_amount),  # Stripe amount is in cents
                currency=price.currency,
                stripe_payment_intent_id=session.payment_intent,
                metadata=metadata
            )

            # Mark payment as completed and grant access
            payment.complete_payment()

            # Check if this is a verification service and update user field
            self._handle_verification_service_payment(service, customer.user)

            self._log_operation(f"Successfully processed service payment {payment.id}")
            return True

        except Exception as e:
            logger.error(f"Error handling service payment: {str(e)}")
            return False

    def _handle_subscription_service_payment(self, session, customer, metadata):
        """Handle subscription service payment (including verification services)"""
        try:
            self._log_operation(f"Processing subscription service payment for session {session.id}")

            # Get the price from metadata
            price_id = metadata.get('price_id')
            if not price_id:
                logger.error(f"No price_id found in metadata for session {session.id}")
                return False

            # Get the price and service
            from ..models.product import Price

            try:
                price = Price.objects.get(id=price_id)
                logger.info(f"Price found for price_id {price_id}")
                product = price.product
                logger.info(f"Product found for price_id {price_id}, product_type: {product.product_type}")

                # Get the appropriate content model based on product type
                content_model = None
                if product.product_type == 'subscription':
                    content_model = product.subscription_plan
                elif product.product_type == 'service':
                    content_model = product.service
                elif product.product_type == 'solution':
                    content_model = product.solution

                logger.info(f"Content model found for price_id {price_id}: {content_model}")

                if not content_model:
                    logger.error(f"No content model found for price_id {price_id}, product_type: {product.product_type}")
                    return False

            except Price.DoesNotExist:
                logger.error(f"Price not found for price_id {price_id}")
                return False
            except AttributeError as e:
                logger.error(f"Error accessing price/product/content_model for price_id {price_id}: {str(e)}")
                return False

            # Create subscription record using subscription service
            subscription_service = self._get_subscription_service()

            # Get the Stripe subscription from the session
            if hasattr(session, 'subscription') and session.subscription:
                stripe_subscription = self._make_stripe_request(
                    stripe.Subscription.retrieve,
                    session.subscription
                )

                # Handle subscription creation
                subscription_service.handle_subscription_created(stripe_subscription)

                # Check if this is a verification service and update user field
                self._handle_verification_service_payment(content_model, customer.user)

                self._log_operation(f"Successfully processed subscription service payment for session {session.id}")
                return True
            else:
                logger.error(f"No subscription found in session {session.id}")
                return False

        except Exception as e:
            logger.error(f"Error handling subscription service payment: {str(e)}")
            return False

    def _handle_verification_service_payment(self, content_model, user):
        """Handle verification service specific logic for both Service and SubscriptionPlan"""
        try:
            # Get the name from the content model (works for both Service and SubscriptionPlan)
            content_name = getattr(content_model, 'name', '')

            # Check if this is a verification service by multiple criteria
            is_verification_service = (
                'verification' in content_name.lower() or
                (hasattr(content_model, 'service_code') and content_model.service_code and 'verification' in content_model.service_code.lower()) or
                'verify' in content_name.lower() or
                content_name.lower() == 'verification services'
            )

            if is_verification_service:
                user.paid_for_verification = True
                user.save()
                self._log_operation(f"Updated paid_for_verification=True for user {user.id}")
                logger.info(f"User {user.id} paid for verification service: {content_name}")
                return True
            else:
                logger.debug(f"Content '{content_name}' is not a verification service")
                return False

        except Exception as e:
            logger.error(f"Error handling verification service payment: {str(e)}")
            # Don't raise here as the main payment was successful
            return False

    def _get_customer_service(self):
        """Get customer service instance"""
        from .customer_service import CustomerService
        return CustomerService()
    
    def _get_payment_service(self):
        """Get payment service instance"""
        from .payment_service import PaymentService
        return PaymentService()
    
    def _get_subscription_service(self):
        """Get subscription service instance"""
        from .subscription_service import SubscriptionService
        return SubscriptionService()
    
    def _get_enterprise_service(self):
        """Get enterprise service instance"""
        from .enterprise_service import EnterpriseService
        return EnterpriseService()

    def _get_appointment_payment_service(self):
        """Get appointment payment service instance"""
        from .appointment_payment_service import AppointmentPaymentService
        return AppointmentPaymentService()

    def handle_account_updated(self, account):
        """
        Handle account.updated event from Stripe Connect

        Updates UserPaymentProfile and DoctorConsultationProfile based on
        Stripe Connect account status changes.

        Args:
            account: Stripe Account object

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self._log_operation(f"Processing account updated: {account.id}")

            # Find the UserPaymentProfile by stripe_account_id
            from ..models.customer import UserPaymentProfile

            try:
                profile = UserPaymentProfile.objects.get(stripe_account_id=account.id)
            except UserPaymentProfile.DoesNotExist:
                logger.warning(f"UserPaymentProfile not found for Stripe account {account.id}")
                return False

            # Update payment profile with current account status
            profile.charges_enabled = getattr(account, 'charges_enabled', False)
            profile.payouts_enabled = getattr(account, 'payouts_enabled', False)
            profile.details_submitted = getattr(account, 'details_submitted', False)

            # Update verification status if account is fully enabled
            if (profile.charges_enabled and
                profile.payouts_enabled and
                profile.details_submitted):
                if not profile.is_verified:
                    profile.verify_account()  # This sets is_verified=True and verification_date
                    logger.info(f"Account {account.id} verified for user {profile.user.email}")
            else:
                # If account capabilities are disabled, mark as unverified
                if profile.is_verified:
                    profile.is_verified = False
                    profile.verification_date = None
                    logger.info(f"Account {account.id} unverified for user {profile.user.email}")

            profile.save()

            # Update DoctorConsultationProfile if user is a doctor
            if hasattr(profile.user, 'consultation_profile'):
                consultation_profile = profile.user.consultation_profile

                # Update stripe_account_setup based on account capabilities
                stripe_setup_complete = (
                    profile.charges_enabled and
                    profile.payouts_enabled and
                    profile.details_submitted
                )

                if consultation_profile.stripe_account_setup != stripe_setup_complete:
                    consultation_profile.stripe_account_setup = stripe_setup_complete
                    consultation_profile.save()

                    logger.info(
                        f"Updated consultation profile stripe_account_setup={stripe_setup_complete} "
                        f"for doctor {profile.user.email}"
                    )

            self._log_operation(f"Successfully updated account status for {account.id}")
            return True

        except Exception as e:
            logger.error(f"Error handling account updated for {account.id}: {str(e)}")
            return False

    def handle_account_deauthorized(self, account):
        """
        Handle account.application.deauthorized event from Stripe Connect

        Deactivates UserPaymentProfile and DoctorConsultationProfile when
        a Connect account is deauthorized.

        Args:
            account: Stripe Account object

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self._log_operation(f"Processing account deauthorized: {account.id}")

            # Find the UserPaymentProfile by stripe_account_id
            from ..models.customer import UserPaymentProfile

            try:
                profile = UserPaymentProfile.objects.get(stripe_account_id=account.id)
            except UserPaymentProfile.DoesNotExist:
                logger.warning(f"UserPaymentProfile not found for Stripe account {account.id}")
                return False

            # Deactivate payment profile
            profile.is_verified = False
            profile.charges_enabled = False
            profile.payouts_enabled = False
            profile.details_submitted = False
            profile.verification_date = None
            profile.update_status('inactive')  # From StatusMixin
            profile.save()

            logger.info(f"Deactivated payment profile for user {profile.user.email}")

            # Deactivate DoctorConsultationProfile if user is a doctor
            if hasattr(profile.user, 'consultation_profile'):
                consultation_profile = profile.user.consultation_profile
                consultation_profile.stripe_account_setup = False
                consultation_profile.is_active = False
                consultation_profile.save()

                logger.info(f"Deactivated consultation profile for doctor {profile.user.email}")

            # Cancel any pending transfers for this user
            from ..models.payment import UserTransfer
            pending_transfers = UserTransfer.objects.filter(
                receiver=profile.user,
                status__in=['pending', 'processing']
            )

            cancelled_count = 0
            for transfer in pending_transfers:
                try:
                    transfer.status = 'canceled'
                    transfer.save()
                    cancelled_count += 1
                except Exception as e:
                    logger.error(f"Error cancelling transfer {transfer.id}: {str(e)}")

            if cancelled_count > 0:
                logger.info(f"Cancelled {cancelled_count} pending transfers for user {profile.user.email}")

            self._log_operation(f"Successfully handled account deauthorization for {account.id}")
            return True

        except Exception as e:
            logger.error(f"Error handling account deauthorized for {account.id}: {str(e)}")
            return False
